'use client';

import { createClient } from '@supabase/supabase-js';
import { env } from './env';
import { parseCSV } from './csv-parser';

export type Charity = {
  id: string;
  name: string;
  description: string;
  mission: string;
  category: string[];
  location: string;
  website: string;
  impact_metrics: {
    beneficiaries_helped: number;
    programs_completed: number;
    donations_received: number;
  };
  financials: {
    total_donations: number;
    program_expenses: number;
    administrative_expenses: number;
  };
  ai_enriched_description?: string;
  created_at: string;
  updated_at: string;
};

export type Profile = {
  id: string;
  user_id: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  donations: {
    charity_id: string;
    amount: number;
    date: string;
  }[];
  created_at: string;
};

// Initialize Supabase client with fallback for development
const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseAnonKey = env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'fallback-key';

class FallbackSupabaseClient {
  private charities: Charity[] = [];
  private profiles: { [key: string]: Profile } = {};
  private currentUser: { id: string; email: string } | null = null;

  async loadCharities() {
    try {
      const response = await fetch('/Social-Welfare-Subvention-Allocations-to-NGOs.csv');
      const csvContent = await response.text();
      this.charities = parseCSV(csvContent);
    } catch (error) {
      console.error('Failed to load CSV data:', error);
      this.charities = [];
    }
  }

  from(table: string) {
    return {
      select: () => {
        if (table === 'charities') {
          return Promise.resolve({ data: this.charities, error: null });
        }
        return Promise.resolve({ data: [], error: null });
      }
    };
  }

  auth = {
    signUp: async ({ email, password }: { email: string; password: string }) => {
      const userId = `user_${Date.now()}`;
      this.currentUser = { id: userId, email };
      this.profiles[userId] = {
        id: userId,
        user_id: userId,
        username: email.split('@')[0],
        full_name: email.split('@')[0],
        donations: [],
        created_at: new Date().toISOString(),
      };
      return { data: { user: this.currentUser }, error: null };
    },
    signIn: async ({ email }: { email: string }) => {
      const userId = `user_${Date.now()}`;
      this.currentUser = { id: userId, email };
      if (!this.profiles[userId]) {
        this.profiles[userId] = {
          id: userId,
          user_id: userId,
          username: email.split('@')[0],
          full_name: email.split('@')[0],
          donations: [],
          created_at: new Date().toISOString(),
        };
      }
      return { data: { user: this.currentUser }, error: null };
    },
    signOut: async () => {
      this.currentUser = null;
      return { error: null };
    },
    getSession: () => {
      return Promise.resolve({
        data: {
          session: this.currentUser ? {
            user: this.currentUser
          } : null
        },
        error: null
      });
    },
    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      callback('SIGNED_OUT', null);
      return {
        data: { subscription: { unsubscribe: () => {} } }
      };
    }
  };
}

// Create client with fallback support
const client = supabaseUrl === 'http://localhost:54321' 
  ? new FallbackSupabaseClient()
  : createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
      },
    });

// Initialize fallback data if using fallback client
if (client instanceof FallbackSupabaseClient) {
  client.loadCharities();
}

export const supabase = client;
