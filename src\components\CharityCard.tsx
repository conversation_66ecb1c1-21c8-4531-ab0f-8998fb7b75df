'use client';

import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import type { Charity } from '@/lib/supabase';

interface CharityCardProps {
  charity: Charity;
  locale: string;
}

export default function CharityCard({ charity, locale }: CharityCardProps) {
  const { t } = useTranslation();
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-transform hover:scale-102">
      <div className="p-6">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
          {charity.name}
        </h3>
        
        {/* Categories */}
        <div className="flex flex-wrap gap-2 mb-4">
          {charity.category.map((cat) => (
            <span
              key={cat}
              className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 text-sm rounded-full"
            >
              {cat}
            </span>
          ))}
        </div>
        
        {/* Description */}
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          {charity.description}
        </p>
        
        {/* Impact Metrics */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {charity.impact_metrics.beneficiaries_helped.toLocaleString()}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('beneficiariesHelped')}
            </p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {charity.impact_metrics.programs_completed.toLocaleString()}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('programsCompleted')}
            </p>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex space-x-4">
          <Link
            href={`/${locale}/charities/${charity.id}`}
            className="flex-1 text-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            {t('learn_more')}
          </Link>
          <Link
            href={`/${locale}/charities/${charity.id}/donate`}
            className="flex-1 text-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            {t('donate')}
          </Link>
        </div>
      </div>
    </div>
  );
}
