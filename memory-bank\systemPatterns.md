# System Patterns

## System Architecture
- **Monolithic Frontend**: The application seems to be a single-page application built with Next.js.
- **Data Processing Layer**: Python/TypeScript scripts are used for data ingestion and processing, enriching raw data into a structured format.
- **Data Storage**: Processed charity data is stored locally in JSON files.
- **API Layer**: Next.js API routes (`src/pages/api`) serve the processed data to the frontend.

## Key Technical Decisions
- **Next.js**: Chosen for its hybrid rendering capabilities (SSR, SSG, ISR) and API routes for backend functionalities.
- **TypeScript**: For type safety and improved code maintainability.
- **OpenAI/Grok xAI Integration**: For advanced data enrichment and categorization.
- **CSV as Data Source**: Simple and easily editable raw data source.
- **JSON for Processed Data**: Lightweight and human-readable format for frontend consumption.

## Design Patterns in Use
- **Service Pattern**: `processCharityData` can be seen as a service that transforms raw CSV into structured charity objects.
- **API Routes**: Next.js API routes provide a clear separation between frontend and backend concerns.
- **Component-Based Architecture**: React components (e.g., `CharityList`, `CharityDetail`) are used to build the UI.

## Component Relationships
- `scripts/generateCharities.ts` orchestrates the data processing, calling `processCharityData`.
- `processCharityData.ts` interacts with external AI services (`openai.ts`, `grokai.ts`) for data enrichment.
- Frontend components (`src/components/charity/CharityList.js`, `src/components/charity/CharityDetail.js`) consume data provided by API routes.
