'use client';

import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { useTheme } from '@/components/ThemeProvider';

export default function Home() {
  const { t } = useTranslation();
  const { theme, toggleTheme } = useTheme();

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="text-center py-12 px-4">
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-blue-600 dark:text-blue-400 mb-6">
          {t('featured')}
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Discover impactful charitable organizations and track their real-world results.
        </p>
      </section>

      {/* Features Grid */}
      <section className="grid md:grid-cols-3 gap-8">
        {/* Search and Filter */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            Smart Discovery
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Find charities by cause, location, or impact metrics with our intelligent search system.
          </p>
          <Link
            href="/charities"
            className="inline-block bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
          >
            {t('search')}
          </Link>
        </div>

        {/* Impact Tracking */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            Impact Tracking
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            See real-world results with transparent impact metrics and financial data.
          </p>
          <button
            className="inline-block bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
          >
            {t('impact')}
          </button>
        </div>

        {/* Secure Donations */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            Secure Donations
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Support your chosen causes with secure, easy-to-track donations through Stripe.
          </p>
          <button
            className="inline-block bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
          >
            {t('donate')}
          </button>
        </div>
      </section>

      {/* Theme Toggle */}
      <div className="fixed bottom-4 right-4">
        <button
          onClick={toggleTheme}
          className="p-3 rounded-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          {theme === 'light' ? '🌙' : '☀️'}
        </button>
      </div>
    </div>
  );
}
