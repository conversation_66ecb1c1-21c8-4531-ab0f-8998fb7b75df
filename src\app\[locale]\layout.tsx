'use client';

import { useEffect } from 'react';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/lib/i18n';
import Navigation from '@/components/Navigation';
import ThemeProvider from '@/components/ThemeProvider';

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: {
    locale: string;
  };
}

export default function LocaleLayout({ children, params: { locale } }: LocaleLayoutProps) {
  useEffect(() => {
    if (i18n.language !== locale) {
      i18n.changeLanguage(locale);
    }
  }, [locale]);

  return (
    <I18nextProvider i18n={i18n}>
      <ThemeProvider>
        <div className="min-h-screen pt-16">
          <Navigation locale={locale} />
          <div className="container mx-auto px-4 py-8">
            {children}
          </div>
        </div>
      </ThemeProvider>
    </I18nextProvider>
  );
}
