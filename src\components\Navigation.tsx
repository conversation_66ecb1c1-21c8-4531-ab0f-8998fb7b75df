import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

type NavLinkProps = {
  href: string;
  children: React.ReactNode;
  className?: string;
};

const NavLink = ({ href, children, className = '' }: NavLinkProps) => {
  const pathname = usePathname();
  const isActive = pathname === href;
  
  return (
    <Link
      href={href}
      className={`px-4 py-2 rounded-lg transition-colors ${
        isActive
          ? 'bg-blue-500 text-white'
          : 'text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-800'
      } ${className}`}
    >
      {children}
    </Link>
  );
};

export default function Navigation({ locale }: { locale: string }) {
  const { t } = useTranslation();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setIsAuthenticated(!!session);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const switchLanguage = (newLocale: string) => {
    // Get current path without locale prefix and leading slash
    const pathname = window.location.pathname;
    const pathWithoutLocale = pathname.replace(/^\/[^/]+/, '');
    
    // Construct new path with new locale
    window.location.href = `/${newLocale}${pathWithoutLocale}`;
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 shadow-lg">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and primary navigation */}
          <div className="flex items-center space-x-4">
            <Link href={`/${locale}`} className="text-xl font-bold text-blue-500">
              Charity Marketplace
            </Link>
            <div className="hidden md:flex space-x-2">
              <NavLink href={`/${locale}`}>{t('home')}</NavLink>
              <NavLink href={`/${locale}/charities`}>{t('charities')}</NavLink>
              <NavLink href={`/${locale}/about`}>{t('about')}</NavLink>
            </div>
          </div>

          {/* Auth and language selection */}
          <div className="flex items-center space-x-4">
            {/* Language switcher */}
            <select
              value={locale}
              onChange={(e) => switchLanguage(e.target.value)}
              className="px-2 py-1 rounded border dark:bg-gray-800 dark:text-white"
            >
              <option value="en">English</option>
              <option value="zh_hk">繁體中文</option>
            </select>

            {/* Auth buttons */}
            <div className="flex space-x-2">
              {isAuthenticated ? (
                <>
                  <NavLink href={`/${locale}/profile`}>{t('profile')}</NavLink>
                  <button
                    onClick={() => supabase.auth.signOut()}
                    className="px-4 py-2 text-red-500 hover:text-red-600 dark:text-red-400"
                  >
                    {t('logout')}
                  </button>
                </>
              ) : (
                <>
                  <NavLink href={`/${locale}/login`}>{t('login')}</NavLink>
                  <NavLink
                    href={`/${locale}/signup`}
                    className="bg-blue-500 text-white hover:bg-blue-600"
                  >
                    {t('signup')}
                  </NavLink>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
