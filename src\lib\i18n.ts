'use client';

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

export const defaultNS = 'common';
export const fallbackLng = 'en';

export const resources = {
  en: {
    common: {
      // Navigation
      home: 'Home',
      charities: 'Charities',
      about: 'About',
      login: 'Login',
      signup: 'Sign Up',
      profile: 'Profile',
      logout: 'Logout',

      // Charity sections
      featured: 'Featured Charities',
      categories: 'Categories',
      search: 'Search charities...',
      filter: 'Filter',
      sortBy: 'Sort by',

      // Charity details
      mission: 'Mission',
      impact: 'Impact',
      financials: 'Financials',
      donate: 'Donate',
      beneficiariesHelped: 'Beneficiaries Helped',
      programsCompleted: 'Programs Completed',
      totalDonations: 'Total Donations',
      programExpenses: 'Program Expenses',
      adminExpenses: 'Administrative Expenses',

      // Donation
      donationAmount: 'Donation Amount',
      cardDetails: 'Card Details',
      submit: 'Submit Donation',
      success: 'Thank you for your donation!',
      error: 'An error occurred',

      // Auth
      emailLabel: 'Email',
      passwordLabel: 'Password',
      forgotPassword: 'Forgot Password?',
      createAccount: 'Create Account',
      learn_more: 'Learn More',
    },
  },
  zh_hk: {
    common: {
      // Navigation
      home: '主頁',
      charities: '慈善機構',
      about: '關於',
      login: '登入',
      signup: '註冊',
      profile: '個人資料',
      logout: '登出',

      // Charity sections
      featured: '精選慈善機構',
      categories: '類別',
      search: '搜尋慈善機構...',
      filter: '篩選',
      sortBy: '排序方式',

      // Charity details
      mission: '使命',
      impact: '影響力',
      financials: '財務資料',
      donate: '捐款',
      beneficiariesHelped: '受惠人數',
      programsCompleted: '完成項目',
      totalDonations: '總捐款',
      programExpenses: '項目支出',
      adminExpenses: '行政支出',

      // Donation
      donationAmount: '捐款金額',
      cardDetails: '信用卡資料',
      submit: '確認捐款',
      success: '感謝您的捐款！',
      error: '發生錯誤',

      // Auth
      emailLabel: '電郵',
      passwordLabel: '密碼',
      forgotPassword: '忘記密碼？',
      createAccount: '建立帳戶',
      learn_more: '了解更多',
    },
  },
};

if (typeof window !== 'undefined') {
  i18n.use(initReactI18next).init({
    resources,
    defaultNS,
    fallbackLng,
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: false,
    },
  });
}

export default i18n;
