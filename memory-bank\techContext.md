# Technical Context

## Technologies Used
- **Frontend Framework**: Next.js (React)
- **Styling**: Tailwind CSS
- **TypeScript**: For main application logic and type safety.
- **Node.js**: Runtime environment for development and build processes.
- **CSV Parsing**: `fs.readFileSync` for reading CSV files.
- **AI Integration**: OpenAI API for `getCategoryFromName` and `enrichCharityDescription` (though `grokai.ts` is also present).
- **Internationalization**: `next-i18next` for multilingual support.
- **Database**: Prisma (though not actively used for charity data storage from CSV, `prisma/schema.prisma` suggests it's available for other data).

## Development Setup
- **Package Manager**: npm/yarn (based on `package.json` and `package-lock.json`).
- **Environment Variables**: dotenv for managing API keys and other configurations.
- **Build Process**: Next.js build (`next build`).
- **Local Development**: Next.js development server (`next dev`).

## Technical Constraints
- Reliance on external AI APIs for data enrichment, which might incur costs or rate limits.
- CSV file format for raw data requires specific parsing logic.
- Multilingual support adds complexity to content management.

## Dependencies
- Refer to `package.json` for a comprehensive list of project dependencies. Key dependencies identified include:
    - `next`
    - `react`, `react-dom`
    - `typescript`
    - `tailwindcss`, `postcss`, `autoprefixer`
    - `openai` (likely for AI integration)
    - `dotenv`
    - `i18next`, `react-i18next`, `next-i18next`
