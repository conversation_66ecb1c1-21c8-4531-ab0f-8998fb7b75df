'use client';

import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import CharityCard from '@/components/CharityCard';
import { supabase } from '@/lib/supabase';
import type { Charity } from '@/lib/supabase';

interface FiltersState {
  search: string;
  categories: string[];
  sortBy: 'name' | 'beneficiaries' | 'donations';
}

export default function CharitiesPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const { t } = useTranslation();
  const [charities, setCharities] = useState<Charity[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<FiltersState>({
    search: '',
    categories: [],
    sortBy: 'name',
  });

  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    loadCharities();
  }, []);

  async function loadCharities() {
    try {
      const { data, error } = await supabase
        .from('charities')
        .select('*');

      if (error) throw error;

      setCharities(data || []);

      // Extract unique categories
      const allCategories = data?.flatMap(charity => charity.category) || [];
      const uniqueCategories = [...new Set(allCategories)];
      setCategories(uniqueCategories);
    } catch (error) {
      console.error('Error loading charities:', error);
    } finally {
      setLoading(false);
    }
  }

  const filteredCharities = charities
    .filter(
      charity =>
        charity.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        charity.description.toLowerCase().includes(filters.search.toLowerCase())
    )
    .filter(
      charity =>
        filters.categories.length === 0 ||
        charity.category.some(cat => filters.categories.includes(cat))
    )
    .sort((a, b) => {
      switch (filters.sortBy) {
        case 'beneficiaries':
          return b.impact_metrics.beneficiaries_helped - a.impact_metrics.beneficiaries_helped;
        case 'donations':
          return b.financials.total_donations - a.financials.total_donations;
        default:
          return a.name.localeCompare(b.name);
      }
    });

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div>
            <input
              type="text"
              placeholder={t('search')}
              value={filters.search}
              onChange={e => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="w-full px-4 py-2 rounded-lg border dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* Categories */}
          <div>
            <select
              multiple
              value={filters.categories}
              onChange={e => {
                const selected = Array.from(e.target.selectedOptions, option => option.value);
                setFilters(prev => ({ ...prev, categories: selected }));
              }}
              className="w-full px-4 py-2 rounded-lg border dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Sort */}
          <div>
            <select
              value={filters.sortBy}
              onChange={e => setFilters(prev => ({ ...prev, sortBy: e.target.value as FiltersState['sortBy'] }))}
              className="w-full px-4 py-2 rounded-lg border dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="name">Name</option>
              <option value="beneficiaries">Beneficiaries Helped</option>
              <option value="donations">Total Donations</option>
            </select>
          </div>
        </div>
      </div>

      {/* Charity Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCharities.map(charity => (
          <CharityCard key={charity.id} charity={charity} locale={locale} />
        ))}
      </div>

      {/* No Results */}
      {filteredCharities.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-600 dark:text-gray-300">No charities found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}
