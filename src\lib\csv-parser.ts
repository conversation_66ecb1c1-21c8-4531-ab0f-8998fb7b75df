// @ts-nocheck
'use client';

import { Charity } from './supabase';

export function parseCSV(csvContent: string): Charity[] {
  try {
    const regex = new RegExp(`[\\r\
]+`);
    const rows = csvContent.split(regex).filter(row => row.trim());
    const data = rows.slice(1).map(row => {
      const columns = row.split('	');
      
      // Convert allocation amount (handle both formats in the CSV)
      let allocation = parseFloat(columns[9]);
      if (isNaN(allocation)) {
        const fallback = columns[9]?.trim() || '0';
        allocation = parseFloat(fallback.replace(/[^0-9.]/g, ''));
      }
      if (isNaN(allocation)) allocation = 0;

      // Create charity object
      return {
        id: columns[3], // NGO Code
        name: columns[6], // NGO Name (English)
        description: `A registered NGO providing social welfare services in Hong Kong.`,
        mission: `Supporting communities through ${columns[0]} subvention programmes.`,
        category: ['Social Welfare', 'NGO'],
        location: 'Hong Kong',
        website: 'https://www.swd.gov.hk',
        impact_metrics: {
          beneficiaries_helped: Math.floor(allocation * 1000), // Estimate based on allocation
          programs_completed: Math.floor(allocation * 0.1), // Estimate
          donations_received: Math.floor(allocation * 0.05), // Estimate
        },
        financials: {
          total_donations: allocation * 1000000, // Convert to actual amount
          program_expenses: allocation * 800000, // Estimate 80% program expenses
          administrative_expenses: allocation * 200000, // Estimate 20% admin expenses
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-06-21T00:00:00Z',
      } as Charity;
    });

    return data;
  } catch (error) {
    console.error('Error parsing CSV:', error);
    return [];
  }
}
