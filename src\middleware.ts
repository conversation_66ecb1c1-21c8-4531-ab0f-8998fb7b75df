import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const supportedLocales = ['en', 'zh_hk'];
const defaultLocale = 'en';

function getLocale(request: NextRequest): string {
  // Get locale from pathname
  const pathname = request.nextUrl.pathname;
  const pathnameLocale = supportedLocales.find(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );
  
  if (pathnameLocale) return pathnameLocale;

  // Get locale from accept-language header
  const acceptLanguage = request.headers.get('accept-language');
  if (acceptLanguage) {
    const [browserLocale] = acceptLanguage.split(',');
    if (browserLocale) {
      const baseLocale = browserLocale.split('-')[0].toLowerCase();
      if (supportedLocales.includes(baseLocale)) {
        return baseLocale;
      }
      // Special case for Traditional Chinese
      if (baseLocale === 'zh' && browserLocale.toLowerCase().includes('hk')) {
        return 'zh_hk';
      }
    }
  }

  return defaultLocale;
}

export function middleware(request: NextRequest) {
  // Skip middleware for api routes and static files
  if (
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.includes('/.')
  ) {
    return;
  }

  const locale = getLocale(request);
  const pathname = request.nextUrl.pathname;

  // If the pathname already starts with a locale, don't modify the request
  if (supportedLocales.some(loc => pathname.startsWith(`/${loc}`))) {
    return;
  }

  // Redirect to locale-prefixed path
  const newUrl = new URL(`/${locale}${pathname}`, request.url);
  newUrl.search = request.nextUrl.search;
  return NextResponse.redirect(newUrl);
}

// Match all pathnames except static files and API routes
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api/ (API routes)
     * 2. /_next/ (Next.js internals)
     * 3. /favicon.ico, /sitemap.xml (static files)
     */
    '/((?!api|_next|favicon.ico|sitemap.xml).*)',
  ],
};
