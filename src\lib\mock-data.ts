import { Charity, Profile } from './supabase';

export const mockCharities: Charity[] = [
  {
    id: '1',
    name: 'Ocean Conservation Foundation',
    description: 'Working to protect marine ecosystems and endangered species.',
    mission: 'To preserve and protect our oceans for future generations.',
    category: ['Environment', 'Conservation'],
    location: 'Hong Kong',
    website: 'https://example.com/ocf',
    impact_metrics: {
      beneficiaries_helped: 50000,
      programs_completed: 120,
      donations_received: 1500,
    },
    financials: {
      total_donations: 2500000,
      program_expenses: 2000000,
      administrative_expenses: 250000,
    },
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-06-21T00:00:00Z',
  },
  {
    id: '2',
    name: 'Youth Education Alliance',
    description: 'Providing educational opportunities to underprivileged youth.',
    mission: 'To empower youth through quality education and mentorship.',
    category: ['Education', 'Youth Development'],
    location: 'Hong Kong',
    website: 'https://example.com/yea',
    impact_metrics: {
      beneficiaries_helped: 75000,
      programs_completed: 250,
      donations_received: 2000,
    },
    financials: {
      total_donations: 3500000,
      program_expenses: 3000000,
      administrative_expenses: 300000,
    },
    created_at: '2023-02-01T00:00:00Z',
    updated_at: '2023-06-21T00:00:00Z',
  },
  {
    id: '3',
    name: 'Elderly Care Network',
    description: 'Supporting seniors with healthcare and social services.',
    mission: 'To ensure dignity and quality of life for the elderly.',
    category: ['Healthcare', 'Elderly Care'],
    location: 'Hong Kong',
    website: 'https://example.com/ecn',
    impact_metrics: {
      beneficiaries_helped: 25000,
      programs_completed: 180,
      donations_received: 1200,
    },
    financials: {
      total_donations: 1800000,
      program_expenses: 1500000,
      administrative_expenses: 200000,
    },
    created_at: '2023-03-01T00:00:00Z',
    updated_at: '2023-06-21T00:00:00Z',
  },
];

export const mockProfiles: Profile[] = [
  {
    id: '1',
    user_id: 'user1',
    username: 'johndoe',
    full_name: 'John Doe',
    avatar_url: 'https://example.com/avatar1.jpg',
    donations: [
      {
        charity_id: '1',
        amount: 1000,
        date: '2023-06-01T00:00:00Z',
      },
    ],
    created_at: '2023-01-01T00:00:00Z',
  },
];
