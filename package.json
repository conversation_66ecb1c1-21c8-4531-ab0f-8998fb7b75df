{"name": "charity-marketplace-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.50.0", "chart.js": "^4.5.0", "i18next": "^25.2.1", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.3", "stripe": "^18.2.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}